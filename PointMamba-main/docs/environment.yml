name: mamba
channels:
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - ca-certificates=2023.12.12=h06a4308_0
  - ld_impl_linux-64=2.38=h1181459_1
  - libffi=3.4.4=h6a678d5_0
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - ncurses=6.4=h6a678d5_0
  - openssl=3.0.12=h7f8727e_0
  - pip=23.3.1=py39h06a4308_0
  - python=3.9.18=h955ad1f_0
  - readline=8.2=h5eee18b_0
  - setuptools=68.2.2=py39h06a4308_0
  - sqlite=3.41.2=h5eee18b_0
  - tk=8.6.12=h1ccaba5_0
  - wheel=0.41.2=py39h06a4308_0
  - xz=5.4.5=h5eee18b_0
  - zlib=1.2.13=h5eee18b_0
  - pip:
      - accelerate==0.26.1
      - addict==2.4.0
      - ansi2html==1.9.1
      - argparse==1.4.0
      - astor==0.8.1
      - asttokens==2.4.1
      - attrs==23.2.0
      - automat==22.10.0
      - blinker==1.7.0
      - buildtools==1.0.6
      - calflops==0.2.9
      - causal-conv1d==1.1.1
      - certifi==2023.11.17
      - charset-normalizer==3.3.2
      - click==8.1.7
      - cloudpickle==3.0.0
      - colorama==0.4.6
      - comm==0.2.1
      - configargparse==1.7
      - constantly==23.10.4
      - contextlib2==21.6.0
      - contourpy==1.2.0
      - cycler==0.12.1
      - cython==0.29.15
      - dash==2.14.2
      - dash-core-components==2.0.0
      - dash-html-components==2.0.0
      - dash-table==5.0.0
      - decorator==5.1.1
      - docopt==0.6.2
      - easydict==1.11
      - einops==0.7.0
      - exceptiongroup==1.2.0
      - executing==2.0.1
      - fastjsonschema==2.19.1
      - filelock==3.10.0
      - flask==3.0.0
      - fonttools==4.47.0
      - fsspec==2023.12.2
      - furl==2.1.3
      - fvcore==0.1.5.post20221221
      - greenlet==3.0.3
      - h5py==3.10.0
      - huggingface-hub==0.20.2
      - hyperlink==21.0.0
      - idna==3.6
      - importlib-metadata==7.0.1
      - importlib-resources==6.1.1
      - incremental==22.10.0
      - iopath==0.1.10
      - ipdb==0.13.13
      - ipython==8.18.1
      - ipywidgets==8.1.1
      - itsdangerous==2.1.2
      - jedi==0.19.1
      - jinja2==3.1.2
      - joblib==1.3.2
      - json-tricks==3.17.3
      - jsonschema==4.20.0
      - jsonschema-specifications==2023.12.1
      - jupyter-core==5.7.1
      - jupyterlab-widgets==3.0.9
      - kiwisolver==1.4.5
      - knn-cuda==0.2
      - mamba-ssm==1.1.1
      - markupsafe==2.1.3
      - matplotlib==3.8.2
      - matplotlib-inline==0.1.6
      - nbformat==5.9.2
      - nest-asyncio==1.5.8
      - ninja==********
      - numpy==1.26.3
      - open3d==0.18.0
      - opencv-python==********
      - orderedmultidict==1.0.1
      - packaging==23.2
      - pandas==2.1.4
      - parso==0.8.3
      - pexpect==4.9.0
      - pillow==10.2.0
      - platformdirs==4.1.0
      - plotly==5.18.0
      - pointnet2-ops==3.0.0
      - portalocker==2.8.2
      - prettytable==3.9.0
      - prompt-toolkit==3.0.43
      - protobuf==4.25.1
      - psutil==5.9.7
      - ptyprocess==0.7.0
      - pure-eval==0.2.2
      - pygments==2.17.2
      - pyparsing==3.1.1
      - pyquaternion==0.9.9
      - python-dateutil==2.8.2
      - pythonwebhdfs==0.2.3
      - pytz==2023.3.post1
      - pyyaml==6.0.1
      - redo==2.0.4
      - referencing==0.32.1
      - regex==2023.12.25
      - requests==2.31.0
      - responses==0.24.1
      - retrying==1.3.4
      - rpds-py==0.16.2
      - safetensors==0.4.1
      - schema==0.7.5
      - scikit-learn==1.3.2
      - scipy==1.11.4
      - simplejson==3.19.2
      - six==1.16.0
      - sqlalchemy==2.0.25
      - stack-data==0.6.3
      - tabulate==0.9.0
      - tenacity==8.2.3
      - tensorboardx==*******
      - termcolor==2.4.0
      - thop==0.1.1-2209072238
      - threadpoolctl==3.2.0
      - timm==0.4.5
      - tokenizers==0.15.0
      - tomli==2.0.1
      - torch==1.13.1+cu117
      - torchaudio==0.13.1+cu117
      - torchstat==0.0.7
      - torchvision==0.14.1+cu117
      - tqdm==4.66.1
      - traitlets==5.14.1
      - transformers==4.36.2
      - transforms3d==0.4.1
      - triton==2.2.0
      - twisted==23.10.0
      - typeguard==2.13.3
      - typing-extensions==4.9.0
      - tzdata==2023.4
      - urllib3==2.1.0
      - wcwidth==0.2.13
      - websockets==12.0
      - werkzeug==3.0.1
      - widgetsnbextension==4.0.9
      - yacs==0.1.8
      - zipp==3.17.0
      - zope-interface==6.1
prefix: /home/<USER>/miniconda3/envs/mamba
