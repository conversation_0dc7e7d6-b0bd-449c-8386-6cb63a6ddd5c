optimizer: {
  type: Adam<PERSON>,
  kwargs: {
    lr: 0.0005,
    weight_decay: 0.05
  } }

scheduler: {
  type: CosLR,
  kwargs: {
    epochs: 150,
    initial_epochs: 10
  } }


dataset: {
  train: { _base_: cfgs/dataset_configs/ModelNet40FewShot.yaml,
           others: { subset: 'train' } },
  val: { _base_: cfgs/dataset_configs/ModelNet40FewShot.yaml,
         others: { subset: 'test' } },
  test: { _base_: cfgs/dataset_configs/ModelNet40.yaml,
          others: { subset: 'test' } } }

model: {
  NAME: PointMambaScan,
  trans_dim: 384,
  depth: 12,
  cls_dim: 40,
  num_heads: 6,
  group_size: 32,
  num_group: 64,
  encoder_dims: 384,
  rms_norm: False,
  drop_path: 0.5,
  drop_out: 0.,
  use_cls_token: False,
  max_head: False,
  avg_head: True,
}

npoints: 1024
total_bs: 32
step_per_update: 1
max_epoch: 150
grad_norm_clip: 10
