optimizer : {
  type: Adam<PERSON>,
  kwargs: {
  lr : 0.001,
  weight_decay : 0.05
  }}

scheduler: {
  type: CosLR,
  kwargs: {
    epochs: 300,
    initial_epochs : 10
  }}

dataset : {
  train : { _base_: cfgs/dataset_configs/ShapeNet-55.yaml,
            others: {subset: 'train', npoints: 1024}},
  val : { _base_: cfgs/dataset_configs/ShapeNet-55.yaml,
            others: {subset: 'test', npoints: 1024}},
  test : { _base_: cfgs/dataset_configs/ShapeNet-55.yaml,
            others: {subset: 'test', npoints: 1024}}}

model : {
  NAME: Point_MAE_Mamba_serializationV2,
  group_size: 32,
  num_group: 64,
  loss: cdl2,
  mamba_config: {
    mask_ratio: 0.6,
    mask_type: 'rand',
    trans_dim: 384,
    encoder_dims: 384,
    depth: 12,
    drop_path_rate: 0.1,
    decoder_depth: 4,
  },
  rms_norm: False,
  drop_path: 0.1,
  drop_out: 0.,
  }

npoints: 1024
total_bs : 128
step_per_update : 1
max_epoch : 300