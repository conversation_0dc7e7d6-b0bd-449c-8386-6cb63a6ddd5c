optimizer: {
  type: Adam<PERSON>,
  kwargs: {
    lr: 0.0003,
    weight_decay: 0.05
  } }

scheduler: {
  type: CosLR,
  kwargs: {
    epochs: 300,
    initial_epochs: 10
  } }

dataset: {
  train: { _base_: cfgs/dataset_configs/ScanObjectNN_objectonly.yaml,
           others: { subset: 'train' } },
  val: { _base_: cfgs/dataset_configs/ScanObjectNN_objectonly.yaml,
         others: { subset: 'test' } },
  test: { _base_: cfgs/dataset_configs/ScanObjectNN_objectonly.yaml,
          others: { subset: 'test' } } }
model: {
  NAME: PointMambaScan,
  trans_dim: 384,
  depth: 12,
  cls_dim: 15,
  num_heads: 6,
  group_size: 32,
  num_group: 128,
  encoder_dims: 384,
  rms_norm: False,
  drop_path: 0.5,
  drop_out: 0.1,
  use_cls_token: False,
  max_head: False,
  avg_head: True,
}


npoints: 2048
total_bs: 32
step_per_update: 1
max_epoch: 300
grad_norm_clip: 10