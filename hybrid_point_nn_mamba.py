import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np
from typing import Optional, List
from functools import partial

# Import components from the original files
from pointnet2_ops import pointnet2_utils
from mamba_ssm.modules.mamba_simple import Mamba
from timm.models.layers import trunc_normal_, DropPath

try:
    from mamba_ssm.ops.triton.layernorm import R<PERSON><PERSON>orm, layer_norm_fn, rms_norm_fn
except ImportError:
    RMSNorm, layer_norm_fn, rms_norm_fn = None, None, None


class HybridPointNNMamba(nn.Module):
    """
    Hybrid architecture combining Point-NN's non-parametric geometric frontend
    with PointMamba's parametric sequence modeling backend.
    
    Architecture Flow:
    1. Non-parametric Geometric Frontend (Point-NN inspired)
    2. Serialization Bridge (PointMamba's space-filling curves)
    3. Parametric Semantic Backend (Mamba blocks)
    """
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        
        # Architecture parameters
        self.input_points = config.input_points  # e.g., 2048
        self.frontend_stages = config.frontend_stages  # e.g., 2
        self.frontend_embed_dim = config.frontend_embed_dim  # e.g., 128
        self.k_neighbors = config.k_neighbors  # e.g., 32
        self.alpha = config.alpha  # e.g., 100
        self.beta = config.beta  # e.g., 1000
        
        self.backend_dim = config.backend_dim  # e.g., 384
        self.backend_depth = config.backend_depth  # e.g., 12
        self.num_classes = config.num_classes
        
        # Build the three components
        self._build_frontend()
        self._build_bridge()
        self._build_backend()
        
        self.apply(self._init_weights)
    
    def _build_frontend(self):
        """Build non-parametric geometric frontend"""
        self.frontend = NonParametricGeometricFrontend(
            input_points=self.input_points,
            num_stages=self.frontend_stages,
            embed_dim=self.frontend_embed_dim,
            k_neighbors=self.k_neighbors,
            alpha=self.alpha,
            beta=self.beta
        )
        
        # Calculate output dimensions after frontend processing
        self.frontend_output_points = self.input_points // (2 ** self.frontend_stages)
        self.frontend_output_dim = self.frontend_embed_dim * (2 ** self.frontend_stages)
    
    def _build_bridge(self):
        """Build serialization bridge"""
        self.bridge = SerializationBridge(
            input_dim=self.frontend_output_dim,
            output_dim=self.backend_dim,
            grid_size=0.02
        )
    
    def _build_backend(self):
        """Build parametric semantic backend"""
        self.backend = ParametricSemanticBackend(
            d_model=self.backend_dim,
            n_layer=self.backend_depth,
            rms_norm=getattr(self.config, 'rms_norm', False),
            drop_path=getattr(self.config, 'drop_path', 0.1)
        )
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.LayerNorm(self.backend_dim),
            nn.Linear(self.backend_dim, 256),
            nn.GELU(),
            nn.Dropout(0.5),
            nn.Linear(256, self.num_classes)
        )
    
    def _init_weights(self, m):
        """Initialize weights following best practices"""
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=0.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
    
    def forward(self, points):
        """
        Forward pass through the hybrid architecture
        
        Args:
            points: Input point cloud [B, N, 3] where N = input_points
            
        Returns:
            logits: Classification logits [B, num_classes]
        """
        # Stage 1: Non-parametric geometric frontend
        geometric_tokens, center_points = self.frontend(points)
        # geometric_tokens: [B, M, C] where M = frontend_output_points
        # center_points: [B, M, 3]
        
        # Stage 2: Serialization bridge
        ordered_tokens = self.bridge(geometric_tokens, center_points)
        # ordered_tokens: [B, 2*M, backend_dim]
        
        # Stage 3: Parametric semantic backend
        contextualized_features = self.backend(ordered_tokens)
        # contextualized_features: [B, 2*M, backend_dim]
        
        # Global aggregation and classification
        global_feature = contextualized_features.mean(dim=1)  # [B, backend_dim]
        logits = self.classifier(global_feature)
        
        return logits


class NonParametricGeometricFrontend(nn.Module):
    """
    Non-parametric frontend based on Point-NN architecture.
    Extracts geometric features without learnable parameters.
    """
    
    def __init__(self, input_points, num_stages, embed_dim, k_neighbors, alpha, beta):
        super().__init__()
        self.input_points = input_points
        self.num_stages = num_stages
        self.embed_dim = embed_dim
        self.k_neighbors = k_neighbors
        self.alpha = alpha
        self.beta = beta
        
        # Initial point embedding (non-parametric)
        self.initial_embed = PosE_Initial(3, embed_dim, alpha, beta)
        
        # Multi-stage hierarchy components
        self.stages = nn.ModuleList()
        current_points = input_points
        current_dim = embed_dim
        
        for i in range(num_stages):
            next_points = current_points // 2
            next_dim = current_dim * 2
            
            stage = GeometricStage(
                input_points=current_points,
                output_points=next_points,
                input_dim=current_dim,
                output_dim=next_dim,
                k_neighbors=k_neighbors,
                alpha=alpha,
                beta=beta
            )
            self.stages.append(stage)
            
            current_points = next_points
            current_dim = next_dim
    
    def forward(self, points):
        """
        Args:
            points: [B, N, 3]
        Returns:
            features: [B, M, C] - geometric tokens
            centers: [B, M, 3] - center coordinates
        """
        B, N, _ = points.shape
        
        # Initial embedding
        x = self.initial_embed(points.transpose(1, 2))  # [B, embed_dim, N]
        xyz = points
        
        # Multi-stage processing
        for stage in self.stages:
            xyz, x = stage(xyz, x)
        
        return x.transpose(1, 2), xyz  # [B, M, C], [B, M, 3]


class GeometricStage(nn.Module):
    """Single stage of geometric feature extraction"""
    
    def __init__(self, input_points, output_points, input_dim, output_dim, 
                 k_neighbors, alpha, beta):
        super().__init__()
        self.input_points = input_points
        self.output_points = output_points
        self.k_neighbors = k_neighbors
        
        # Non-parametric components
        self.geometry_extractor = PosE_Geo(3, output_dim, alpha, beta)
        self.pooling = GeometricPooling(output_dim)
    
    def forward(self, xyz, x):
        """
        Args:
            xyz: [B, N, 3] - point coordinates
            x: [B, C, N] - point features
        Returns:
            new_xyz: [B, M, 3] - sampled coordinates
            new_x: [B, C', M] - aggregated features
        """
        B, N, _ = xyz.shape
        
        # Furthest Point Sampling
        fps_idx = pointnet2_utils.furthest_point_sample(xyz, self.output_points).long()
        new_xyz = index_points(xyz, fps_idx)  # [B, M, 3]
        new_x = index_points(x.transpose(1, 2), fps_idx)  # [B, M, C]
        
        # k-NN grouping
        knn_idx = knn_point(self.k_neighbors, xyz, new_xyz)
        knn_xyz = index_points(xyz, knn_idx)  # [B, M, K, 3]
        knn_x = index_points(x.transpose(1, 2), knn_idx)  # [B, M, K, C]
        
        # Local geometry aggregation
        aggregated_x = self._aggregate_geometry(new_xyz, new_x, knn_xyz, knn_x)
        
        return new_xyz, aggregated_x.transpose(1, 2)  # [B, M, 3], [B, C', M]
    
    def _aggregate_geometry(self, lc_xyz, lc_x, knn_xyz, knn_x):
        """Aggregate local geometric features"""
        B, G, K, C = knn_x.shape
        
        # Normalize features and coordinates
        mean_x = lc_x.unsqueeze(2)  # [B, G, 1, C]
        std_x = torch.std(knn_x - mean_x, dim=2, keepdim=True) + 1e-5
        knn_x_norm = (knn_x - mean_x) / std_x
        
        mean_xyz = lc_xyz.unsqueeze(2)  # [B, G, 1, 3]
        std_xyz = torch.std(knn_xyz - mean_xyz, dim=2, keepdim=True) + 1e-5
        knn_xyz_norm = (knn_xyz - mean_xyz) / std_xyz
        
        # Feature expansion
        expanded_lc_x = lc_x.unsqueeze(2).expand(-1, -1, K, -1)
        knn_x_expanded = torch.cat([knn_x_norm, expanded_lc_x], dim=-1)
        
        # Geometric weighting
        knn_xyz_T = knn_xyz_norm.permute(0, 3, 1, 2)  # [B, 3, G, K]
        knn_x_T = knn_x_expanded.permute(0, 3, 1, 2)  # [B, 2C, G, K]
        
        weighted_x = self.geometry_extractor(knn_xyz_T, knn_x_T)
        
        # Pooling
        aggregated = self.pooling(weighted_x)
        
        return aggregated.transpose(1, 2)  # [B, G, C_out]


class PosE_Initial(nn.Module):
    """Positional encoding for initial point embedding"""
    
    def __init__(self, in_dim, out_dim, alpha, beta):
        super().__init__()
        self.in_dim = in_dim
        self.out_dim = out_dim
        self.alpha = alpha
        self.beta = beta
    
    def forward(self, xyz):
        """
        Args:
            xyz: [B, 3, N]
        Returns:
            pos_embed: [B, out_dim, N]
        """
        B, _, N = xyz.shape
        feat_dim = self.out_dim // (self.in_dim * 2)
        
        feat_range = torch.arange(feat_dim, dtype=torch.float32, device=xyz.device)
        dim_embed = torch.pow(self.alpha, feat_range / feat_dim)
        div_embed = torch.div(self.beta * xyz.unsqueeze(-1), dim_embed)
        
        sin_embed = torch.sin(div_embed)
        cos_embed = torch.cos(div_embed)
        position_embed = torch.stack([sin_embed, cos_embed], dim=4).flatten(3)
        position_embed = position_embed.permute(0, 1, 3, 2).reshape(B, self.out_dim, N)
        
        return position_embed


class PosE_Geo(nn.Module):
    """Positional encoding for local geometry extraction"""
    
    def __init__(self, in_dim, out_dim, alpha, beta):
        super().__init__()
        self.in_dim = in_dim
        self.out_dim = out_dim
        self.alpha = alpha
        self.beta = beta
    
    def forward(self, knn_xyz, knn_x):
        """
        Args:
            knn_xyz: [B, 3, G, K]
            knn_x: [B, C, G, K]
        Returns:
            weighted_x: [B, out_dim, G, K]
        """
        B, _, G, K = knn_xyz.shape
        feat_dim = self.out_dim // (self.in_dim * 2)
        
        feat_range = torch.arange(feat_dim, dtype=torch.float32, device=knn_xyz.device)
        dim_embed = torch.pow(self.alpha, feat_range / feat_dim)
        div_embed = torch.div(self.beta * knn_xyz.unsqueeze(-1), dim_embed)
        
        sin_embed = torch.sin(div_embed)
        cos_embed = torch.cos(div_embed)
        position_embed = torch.stack([sin_embed, cos_embed], dim=5).flatten(4)
        position_embed = position_embed.permute(0, 1, 4, 2, 3).reshape(B, self.out_dim, G, K)
        
        # Geometric weighting
        weighted_x = knn_x + position_embed
        weighted_x = weighted_x * position_embed
        
        return weighted_x


class GeometricPooling(nn.Module):
    """Pooling for geometric features"""
    
    def __init__(self, dim):
        super().__init__()
        self.norm = nn.BatchNorm1d(dim)
        self.activation = nn.GELU()
    
    def forward(self, x):
        """
        Args:
            x: [B, C, G, K]
        Returns:
            pooled: [B, C, G]
        """
        # Max + Mean pooling
        max_pool = x.max(dim=-1)[0]
        mean_pool = x.mean(dim=-1)
        pooled = max_pool + mean_pool
        
        # Normalization and activation
        pooled = self.norm(pooled)
        pooled = self.activation(pooled)
        
        return pooled


class SerializationBridge(nn.Module):
    """Bridge connecting non-parametric frontend to parametric backend"""
    
    def __init__(self, input_dim, output_dim, grid_size=0.02):
        super().__init__()
        self.grid_size = grid_size
        
        # Dimension adaptation
        self.dim_adapter = nn.Linear(input_dim, output_dim)
        
        # Order-specific scaling parameters (lightweight learnable components)
        self.order_scale_forward = OrderScale(output_dim)
        self.order_scale_backward = OrderScale(output_dim)
    
    def forward(self, features, coordinates):
        """
        Args:
            features: [B, M, C_in] - geometric tokens
            coordinates: [B, M, 3] - center coordinates
        Returns:
            serialized_tokens: [B, 2*M, C_out] - ordered sequence
        """
        # Adapt dimensions
        features = self.dim_adapter(features)  # [B, M, C_out]
        
        # Serialize using space-filling curves
        features_forward, _ = self._serialize_with_hilbert(features, coordinates, 'hilbert')
        features_backward, _ = self._serialize_with_hilbert(features, coordinates, 'hilbert-trans')
        
        # Apply order-specific scaling
        features_forward = self.order_scale_forward(features_forward)
        features_backward = self.order_scale_backward(features_backward)
        
        # Concatenate both orders
        serialized_tokens = torch.cat([features_forward, features_backward], dim=1)
        
        return serialized_tokens
    
    def _serialize_with_hilbert(self, features, coordinates, order_type):
        """Serialize features using Hilbert curve ordering"""
        B, M, C = features.shape
        
        # Convert coordinates to grid coordinates
        scaled_coord = coordinates / self.grid_size
        grid_coord = torch.floor(scaled_coord).to(torch.int64)
        min_coord = grid_coord.min(dim=1, keepdim=True)[0]
        grid_coord = grid_coord - min_coord
        
        # Create batch indices
        batch_idx = torch.arange(B, device=coordinates.device).unsqueeze(1).expand(-1, M)
        
        # Serialize (simplified implementation - in practice would use proper Hilbert curve)
        if order_type == 'hilbert':
            # Z-order curve approximation
            z_order = self._compute_z_order(grid_coord)
        else:  # hilbert-trans
            # Transposed Z-order
            z_order = self._compute_z_order(grid_coord.flip(dims=[-1]))
        
        # Sort features according to the computed order
        sorted_indices = torch.argsort(z_order, dim=1)
        sorted_features = torch.gather(features, 1, sorted_indices.unsqueeze(-1).expand(-1, -1, C))
        
        return sorted_features, sorted_indices
    
    def _compute_z_order(self, grid_coord):
        """Compute Z-order (Morton code) for 3D coordinates"""
        x, y, z = grid_coord[..., 0], grid_coord[..., 1], grid_coord[..., 2]
        
        # Simple Z-order approximation
        z_order = x + y * 1000 + z * 1000000
        
        return z_order


class OrderScale(nn.Module):
    """Lightweight order-specific scaling"""
    
    def __init__(self, dim):
        super().__init__()
        self.gamma = nn.Parameter(torch.ones(dim))
        self.beta = nn.Parameter(torch.zeros(dim))
        nn.init.normal_(self.gamma, mean=1.0, std=0.02)
        nn.init.normal_(self.beta, std=0.02)
    
    def forward(self, x):
        return x * self.gamma + self.beta


class ParametricSemanticBackend(nn.Module):
    """Parametric backend using Mamba blocks for sequence modeling"""
    
    def __init__(self, d_model, n_layer, rms_norm=False, drop_path=0.1):
        super().__init__()
        
        # Create drop path rates
        dpr = [x.item() for x in torch.linspace(0, drop_path, n_layer)]
        
        # Mamba blocks
        self.layers = nn.ModuleList([
            self._create_mamba_block(
                d_model=d_model,
                layer_idx=i,
                rms_norm=rms_norm,
                drop_path=dpr[i]
            ) for i in range(n_layer)
        ])
        
        # Final normalization
        self.norm = (nn.LayerNorm if not rms_norm else RMSNorm)(d_model)
    
    def _create_mamba_block(self, d_model, layer_idx, rms_norm, drop_path):
        """Create a single Mamba block"""
        from .block_scan import Block  # Import from the original file
        
        mixer_cls = partial(Mamba, layer_idx=layer_idx)
        norm_cls = partial(nn.LayerNorm if not rms_norm else RMSNorm, eps=1e-5)
        
        block = Block(
            d_model,
            mixer_cls,
            norm_cls=norm_cls,
            fused_add_norm=False,
            residual_in_fp32=False,
            drop_path=drop_path,
        )
        block.layer_idx = layer_idx
        return block
    
    def forward(self, x):
        """
        Args:
            x: [B, L, D] - input sequence
        Returns:
            x: [B, L, D] - contextualized features
        """
        for layer in self.layers:
            x = layer(x)
        
        x = self.norm(x)
        return x


# Utility functions
def index_points(points, idx):
    """Index points according to the given indices"""
    batch_size = points.shape[0]
    device = points.device
    
    batch_indices = torch.arange(batch_size, device=device).view(-1, 1, 1)
    if len(idx.shape) == 3:  # [B, N, K]
        batch_indices = batch_indices.expand(-1, idx.shape[1], idx.shape[2])
        idx = idx + batch_indices * points.shape[1]
        idx = idx.view(-1)
    else:  # [B, N]
        batch_indices = batch_indices.expand(-1, idx.shape[1])
        idx = idx + batch_indices * points.shape[1]
        idx = idx.view(-1)
    
    points_flat = points.view(-1, points.shape[-1])
    result = points_flat[idx].view(batch_size, *idx.shape[1:], -1)
    
    return result


def knn_point(k, reference_pts, query_pts):
    """Find k nearest neighbors"""
    B, N, _ = reference_pts.shape
    _, M, _ = query_pts.shape
    
    # Compute pairwise distances
    ref_expanded = reference_pts.unsqueeze(2)  # [B, N, 1, 3]
    query_expanded = query_pts.unsqueeze(1)    # [B, 1, M, 3]
    
    distances = torch.sum((ref_expanded - query_expanded) ** 2, dim=-1)  # [B, N, M]
    distances = distances.transpose(1, 2)  # [B, M, N]
    
    # Find k nearest neighbors
    _, knn_idx = torch.topk(distances, k, dim=-1, largest=False)  # [B, M, k]
    
    return knn_idx


# Example configuration class
class HybridConfig:
    def __init__(self):
        # Frontend parameters (Point-NN inspired)
        self.input_points = 2048
        self.frontend_stages = 2
        self.frontend_embed_dim = 128
        self.k_neighbors = 32
        self.alpha = 100
        self.beta = 1000
        
        # Backend parameters (Mamba inspired)
        self.backend_dim = 384
        self.backend_depth = 12
        self.rms_norm = False
        self.drop_path = 0.1
        
        # Task parameters
        self.num_classes = 40  # For ModelNet40


# Example usage
def create_hybrid_model():
    config = HybridConfig()
    model = HybridPointNNMamba(config)
    return model


if __name__ == "__main__":
    # Test the model
    model = create_hybrid_model()
    batch_size = 4
    num_points = 2048
    
    # Create dummy input
    points = torch.randn(batch_size, num_points, 3)
    
    # Forward pass
    with torch.no_grad():
        logits = model(points)
        print(f"Input shape: {points.shape}")
        print(f"Output shape: {logits.shape}")
        print("Model created successfully!")
