import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np
from typing import Optional, List
from functools import partial

# Import components from the original files
from pointnet2_ops import pointnet2_utils
from mamba_ssm.modules.mamba_simple import Mamba
from timm.models.layers import trunc_normal_, DropPath

try:
    from mamba_ssm.ops.triton.layernorm import RMSNorm, layer_norm_fn, rms_norm_fn
except ImportError:
    RMSNorm, layer_norm_fn, rms_norm_fn = None, None, None


# Utility functions (from Point-NN model_utils.py)
def square_distance(src, dst):
    """
    Calculate Euclid distance between each two points.
    Input:
        src: source points, [B, N, C]
        dst: target points, [B, M, C]
    Output:
        dist: per-point square distance, [B, N, M]
    """
    B, N, _ = src.shape
    _, M, _ = dst.shape
    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))
    dist += torch.sum(src ** 2, -1).view(B, N, 1)
    dist += torch.sum(dst ** 2, -1).view(B, 1, M)
    return dist


def index_points(points, idx):
    """
    Input:
        points: input points data, [B, N, C]
        idx: sample index data, [B, S] or [B, S, K]
    Return:
        new_points:, indexed points data, [B, S, C] or [B, S, K, C]
    """
    device = points.device
    B = points.shape[0]
    view_shape = list(idx.shape)
    view_shape[1:] = [1] * (len(view_shape) - 1)
    repeat_shape = list(idx.shape)
    repeat_shape[0] = 1
    batch_indices = torch.arange(B, dtype=torch.long).to(device).view(view_shape).repeat(repeat_shape)
    new_points = points[batch_indices, idx, :]
    return new_points


def knn_point(nsample, xyz, new_xyz):
    """
    Input:
        nsample: max sample number in local region
        xyz: all points, [B, N, C]
        new_xyz: query points, [B, S, C]
    Return:
        group_idx: grouped points index, [B, S, nsample]
    """
    sqrdists = square_distance(new_xyz, xyz)
    _, group_idx = torch.topk(sqrdists, nsample, dim=-1, largest=False, sorted=False)
    return group_idx


# Block implementation (from PointMamba block_scan.py)
class Block(nn.Module):
    def __init__(
        self, dim, mixer_cls, norm_cls=nn.LayerNorm, fused_add_norm=False, residual_in_fp32=False, drop_path=0.
    ):
        """
        Simple block wrapping a mixer class with LayerNorm/RMSNorm and residual connection
        """
        super().__init__()
        self.residual_in_fp32 = residual_in_fp32
        self.fused_add_norm = fused_add_norm
        self.mixer = mixer_cls(dim)
        self.norm = norm_cls(dim)

        # drop path
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        if self.fused_add_norm:
            assert RMSNorm is not None, "RMSNorm import fails"
            assert isinstance(
                self.norm, (nn.LayerNorm, RMSNorm)
            ), "Only LayerNorm and RMSNorm are supported for fused_add_norm"

    def forward(self, hidden_states, inference_params=None):
        """Pass the input through the encoder layer."""
        hidden_states = hidden_states + self.drop_path(
            self.mixer(self.norm(hidden_states), inference_params=inference_params))
        return hidden_states

    def allocate_inference_cache(self, batch_size, max_seqlen, dtype=None, **kwargs):
        return self.mixer.allocate_inference_cache(batch_size, max_seqlen, dtype=dtype, **kwargs)


class HybridPointNNMamba(nn.Module):
    """
    Hybrid architecture combining Point-NN's non-parametric geometric frontend
    with PointMamba's parametric sequence modeling backend.
    
    Architecture Flow:
    1. Non-parametric Geometric Frontend (Point-NN inspired)
    2. Serialization Bridge (PointMamba's space-filling curves)
    3. Parametric Semantic Backend (Mamba blocks)
    """
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        
        # Architecture parameters
        self.input_points = config.input_points  # e.g., 2048
        self.frontend_stages = config.frontend_stages  # e.g., 2
        self.frontend_embed_dim = config.frontend_embed_dim  # e.g., 128
        self.k_neighbors = config.k_neighbors  # e.g., 32
        self.alpha = config.alpha  # e.g., 100
        self.beta = config.beta  # e.g., 1000
        
        self.backend_dim = config.backend_dim  # e.g., 384
        self.backend_depth = config.backend_depth  # e.g., 12
        self.num_classes = config.num_classes
        
        # Build the three components
        self._build_frontend()
        self._build_bridge()
        self._build_backend()
        
        self.apply(self._init_weights)
    
    def _build_frontend(self):
        """Build non-parametric geometric frontend"""
        self.frontend = NonParametricGeometricFrontend(
            input_points=self.input_points,
            num_stages=self.frontend_stages,
            embed_dim=self.frontend_embed_dim,
            k_neighbors=self.k_neighbors,
            alpha=self.alpha,
            beta=self.beta
        )
        
        # Calculate output dimensions after frontend processing
        self.frontend_output_points = self.input_points // (2 ** self.frontend_stages)
        self.frontend_output_dim = self.frontend_embed_dim * (2 ** self.frontend_stages)
    
    def _build_bridge(self):
        """Build serialization bridge"""
        self.bridge = SerializationBridge(
            input_dim=self.frontend_output_dim,
            output_dim=self.backend_dim,
            grid_size=0.02
        )
    
    def _build_backend(self):
        """Build parametric semantic backend"""
        self.backend = ParametricSemanticBackend(
            d_model=self.backend_dim,
            n_layer=self.backend_depth,
            rms_norm=getattr(self.config, 'rms_norm', False),
            drop_path=getattr(self.config, 'drop_path', 0.1)
        )
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.LayerNorm(self.backend_dim),
            nn.Linear(self.backend_dim, 256),
            nn.GELU(),
            nn.Dropout(0.5),
            nn.Linear(256, self.num_classes)
        )
    
    def _init_weights(self, m):
        """Initialize weights following best practices"""
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=0.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
    
    def forward(self, points):
        """
        Forward pass through the hybrid architecture
        
        Args:
            points: Input point cloud [B, N, 3] where N = input_points
            
        Returns:
            logits: Classification logits [B, num_classes]
        """
        # Stage 1: Non-parametric geometric frontend
        geometric_tokens, center_points = self.frontend(points)
        # geometric_tokens: [B, M, C] where M = frontend_output_points
        # center_points: [B, M, 3]
        
        # Stage 2: Serialization bridge
        ordered_tokens = self.bridge(geometric_tokens, center_points)
        # ordered_tokens: [B, 2*M, backend_dim]
        
        # Stage 3: Parametric semantic backend
        contextualized_features = self.backend(ordered_tokens)
        # contextualized_features: [B, 2*M, backend_dim]
        
        # Global aggregation and classification
        global_feature = contextualized_features.mean(dim=1)  # [B, backend_dim]
        logits = self.classifier(global_feature)
        
        return logits


class NonParametricGeometricFrontend(nn.Module):
    """
    Non-parametric frontend based on Point-NN architecture.
    Extracts geometric features without learnable parameters.
    """
    
    def __init__(self, input_points, num_stages, embed_dim, k_neighbors, alpha, beta):
        super().__init__()
        self.input_points = input_points
        self.num_stages = num_stages
        self.embed_dim = embed_dim
        self.k_neighbors = k_neighbors
        self.alpha = alpha
        self.beta = beta
        
        # Initial point embedding (non-parametric)
        self.initial_embed = PosE_Initial(3, embed_dim, alpha, beta)
        
        # Multi-stage hierarchy components
        self.stages = nn.ModuleList()
        current_points = input_points
        current_dim = embed_dim
        
        for _ in range(num_stages):
            next_points = current_points // 2
            next_dim = current_dim * 2
            
            stage = GeometricStage(
                input_points=current_points,
                output_points=next_points,
                input_dim=current_dim,
                output_dim=next_dim,
                k_neighbors=k_neighbors,
                alpha=alpha,
                beta=beta
            )
            self.stages.append(stage)
            
            current_points = next_points
            current_dim = next_dim
    
    def forward(self, points):
        """
        Args:
            points: [B, N, 3]
        Returns:
            features: [B, M, C] - geometric tokens
            centers: [B, M, 3] - center coordinates
        """
        # Initial embedding - use coordinates as input to PosE_Initial
        xyz = points  # [B, N, 3]
        x = self.initial_embed(points.transpose(1, 2))  # [B, embed_dim, N]

        # Multi-stage processing
        for stage in self.stages:
            xyz, x = stage(xyz, x)

        return x.transpose(1, 2), xyz  # [B, M, C], [B, M, 3]


class GeometricStage(nn.Module):
    """Single stage of geometric feature extraction"""
    
    def __init__(self, input_points, output_points, input_dim, output_dim,
                 k_neighbors, alpha, beta):
        super().__init__()
        self.input_points = input_points
        self.output_points = output_points
        self.input_dim = input_dim
        self.k_neighbors = k_neighbors

        # Non-parametric components
        self.geometry_extractor = PosE_Geo(3, output_dim, alpha, beta)
        self.pooling = GeometricPooling(output_dim)
    
    def forward(self, xyz, x):
        """
        Args:
            xyz: [B, N, 3] - point coordinates
            x: [B, C, N] - point features
        Returns:
            new_xyz: [B, M, 3] - sampled coordinates
            new_x: [B, C', M] - aggregated features
        """
        # Furthest Point Sampling
        fps_idx = pointnet2_utils.furthest_point_sample(xyz, self.output_points).long()
        new_xyz = index_points(xyz, fps_idx)  # [B, M, 3]
        new_x = index_points(x.transpose(1, 2), fps_idx)  # [B, M, C]

        # k-NN grouping
        knn_idx = knn_point(self.k_neighbors, xyz, new_xyz)
        knn_xyz = index_points(xyz, knn_idx)  # [B, M, K, 3]
        knn_x = index_points(x.transpose(1, 2), knn_idx)  # [B, M, K, C]

        # Local geometry aggregation
        aggregated_x = self._aggregate_geometry(new_xyz, new_x, knn_xyz, knn_x)

        return new_xyz, aggregated_x.transpose(1, 2)  # [B, M, 3], [B, C', M]
    
    def _aggregate_geometry(self, lc_xyz, lc_x, knn_xyz, knn_x):
        """Aggregate local geometric features"""
        _, _, K, _ = knn_x.shape

        # Normalize features and coordinates
        mean_x = lc_x.unsqueeze(2)  # [B, G, 1, C]
        std_x = torch.std(knn_x - mean_x, dim=2, keepdim=True) + 1e-5
        knn_x_norm = (knn_x - mean_x) / std_x

        mean_xyz = lc_xyz.unsqueeze(2)  # [B, G, 1, 3]
        std_xyz = torch.std(knn_xyz - mean_xyz, dim=2, keepdim=True) + 1e-5
        knn_xyz_norm = (knn_xyz - mean_xyz) / std_xyz

        # Feature expansion
        expanded_lc_x = lc_x.unsqueeze(2).expand(-1, -1, K, -1)
        knn_x_expanded = torch.cat([knn_x_norm, expanded_lc_x], dim=-1)

        # Geometric weighting
        knn_xyz_T = knn_xyz_norm.permute(0, 3, 1, 2)  # [B, 3, G, K]
        knn_x_T = knn_x_expanded.permute(0, 3, 1, 2)  # [B, 2C, G, K]

        weighted_x = self.geometry_extractor(knn_xyz_T, knn_x_T)

        # Pooling
        aggregated = self.pooling(weighted_x)

        return aggregated.transpose(1, 2)  # [B, G, C_out]


class PosE_Initial(nn.Module):
    """Positional encoding for initial point embedding"""
    
    def __init__(self, in_dim, out_dim, alpha, beta):
        super().__init__()
        self.in_dim = in_dim
        self.out_dim = out_dim
        self.alpha = alpha
        self.beta = beta
    
    def forward(self, xyz):
        """
        Args:
            xyz: [B, 3, N]
        Returns:
            pos_embed: [B, out_dim, N]
        """
        B, _, N = xyz.shape
        feat_dim = self.out_dim // (self.in_dim * 2)

        feat_range = torch.arange(feat_dim, dtype=torch.float32, device=xyz.device)
        dim_embed = torch.pow(self.alpha, feat_range / feat_dim)
        div_embed = torch.div(self.beta * xyz.unsqueeze(-1), dim_embed)

        sin_embed = torch.sin(div_embed)
        cos_embed = torch.cos(div_embed)
        position_embed = torch.stack([sin_embed, cos_embed], dim=4).flatten(3)
        # position_embed shape: [B, 3, N, feat_dim*2]
        position_embed = position_embed.permute(0, 1, 3, 2)  # [B, 3, feat_dim*2, N]
        position_embed = position_embed.contiguous().view(B, -1, N)  # [B, 3*feat_dim*2, N]

        # If the computed dimension doesn't match out_dim, pad or truncate
        current_dim = position_embed.shape[1]
        if current_dim < self.out_dim:
            # Pad with zeros
            padding = torch.zeros(B, self.out_dim - current_dim, N, device=xyz.device)
            position_embed = torch.cat([position_embed, padding], dim=1)
        elif current_dim > self.out_dim:
            # Truncate
            position_embed = position_embed[:, :self.out_dim, :]

        return position_embed


class PosE_Geo(nn.Module):
    """Positional encoding for local geometry extraction"""
    
    def __init__(self, in_dim, out_dim, alpha, beta):
        super().__init__()
        self.in_dim = in_dim
        self.out_dim = out_dim
        self.alpha = alpha
        self.beta = beta
    
    def forward(self, knn_xyz, knn_x):
        """
        Args:
            knn_xyz: [B, 3, G, K]
            knn_x: [B, C, G, K]
        Returns:
            weighted_x: [B, out_dim, G, K]
        """
        B, _, G, K = knn_xyz.shape
        feat_dim = self.out_dim // (self.in_dim * 2)

        feat_range = torch.arange(feat_dim, dtype=torch.float32, device=knn_xyz.device)
        dim_embed = torch.pow(self.alpha, feat_range / feat_dim)
        div_embed = torch.div(self.beta * knn_xyz.unsqueeze(-1), dim_embed)

        sin_embed = torch.sin(div_embed)
        cos_embed = torch.cos(div_embed)
        position_embed = torch.stack([sin_embed, cos_embed], dim=5).flatten(4)
        # position_embed shape: [B, 3, G, K, feat_dim*2]
        position_embed = position_embed.permute(0, 1, 4, 2, 3)  # [B, 3, feat_dim*2, G, K]
        position_embed = position_embed.contiguous().view(B, -1, G, K)  # [B, 3*feat_dim*2, G, K]

        # If the computed dimension doesn't match out_dim, pad or truncate
        current_dim = position_embed.shape[1]
        if current_dim < self.out_dim:
            # Pad with zeros
            padding = torch.zeros(B, self.out_dim - current_dim, G, K, device=knn_xyz.device)
            position_embed = torch.cat([position_embed, padding], dim=1)
        elif current_dim > self.out_dim:
            # Truncate
            position_embed = position_embed[:, :self.out_dim, :]

        # Geometric weighting
        weighted_x = knn_x + position_embed
        weighted_x = weighted_x * position_embed

        return weighted_x


class GeometricPooling(nn.Module):
    """Pooling for geometric features"""
    
    def __init__(self, dim):
        super().__init__()
        self.norm = nn.BatchNorm1d(dim)
        self.activation = nn.GELU()
    
    def forward(self, x):
        """
        Args:
            x: [B, C, G, K]
        Returns:
            pooled: [B, C, G]
        """
        # Max + Mean pooling
        max_pool = x.max(dim=-1)[0]
        mean_pool = x.mean(dim=-1)
        pooled = max_pool + mean_pool
        
        # Normalization and activation
        pooled = self.norm(pooled)
        pooled = self.activation(pooled)
        
        return pooled


class SerializationBridge(nn.Module):
    """Bridge connecting non-parametric frontend to parametric backend"""
    
    def __init__(self, input_dim, output_dim, grid_size=0.02):
        super().__init__()
        self.grid_size = grid_size
        
        # Dimension adaptation
        self.dim_adapter = nn.Linear(input_dim, output_dim)
        
        # Order-specific scaling parameters (lightweight learnable components)
        self.order_scale_forward = OrderScale(output_dim)
        self.order_scale_backward = OrderScale(output_dim)
    
    def forward(self, features, coordinates):
        """
        Args:
            features: [B, M, C_in] - geometric tokens
            coordinates: [B, M, 3] - center coordinates
        Returns:
            serialized_tokens: [B, 2*M, C_out] - ordered sequence
        """
        # Adapt dimensions
        features = self.dim_adapter(features)  # [B, M, C_out]
        
        # Serialize using space-filling curves
        features_forward, _ = self._serialize_with_hilbert(features, coordinates, 'hilbert')
        features_backward, _ = self._serialize_with_hilbert(features, coordinates, 'hilbert-trans')
        
        # Apply order-specific scaling
        features_forward = self.order_scale_forward(features_forward)
        features_backward = self.order_scale_backward(features_backward)
        
        # Concatenate both orders
        serialized_tokens = torch.cat([features_forward, features_backward], dim=1)
        
        return serialized_tokens
    
    def _serialize_with_hilbert(self, features, coordinates, order_type):
        """Serialize features using Hilbert curve ordering"""
        B, _, C = features.shape

        # Convert coordinates to grid coordinates
        scaled_coord = coordinates / self.grid_size
        grid_coord = torch.floor(scaled_coord).to(torch.int64)

        # Normalize grid coordinates to start from 0
        min_coord = grid_coord.view(-1, 3).min(dim=0)[0]
        grid_coord = grid_coord - min_coord.unsqueeze(0).unsqueeze(0)

        # Compute Hilbert codes for each batch
        hilbert_codes = []
        for b in range(B):
            batch_coords = grid_coord[b]  # [M, 3]

            # Determine depth based on coordinate range
            max_coord = batch_coords.max()
            depth = max(4, int(max_coord.item()).bit_length())  # At least 4 bits
            depth = min(depth, 16)  # Maximum 16 bits to avoid overflow

            # Clamp coordinates to valid range
            max_val = (2 ** depth) - 1
            batch_coords = torch.clamp(batch_coords, 0, max_val)

            if order_type == 'hilbert':
                codes = self._hilbert_encode(batch_coords, depth)
            else:  # hilbert-trans
                # Transpose coordinates for different ordering
                transposed_coords = batch_coords[:, [1, 0, 2]]
                codes = self._hilbert_encode(transposed_coords, depth)

            hilbert_codes.append(codes)

        # Stack codes and sort
        hilbert_codes = torch.stack(hilbert_codes)  # [B, M]
        sorted_indices = torch.argsort(hilbert_codes, dim=1)

        # Sort features according to Hilbert order
        sorted_features = torch.gather(features, 1, sorted_indices.unsqueeze(-1).expand(-1, -1, C))

        return sorted_features, sorted_indices

    def _hilbert_encode(self, coords, depth):
        """Encode 3D coordinates using Hilbert curve"""
        # Simple implementation using Z-order as approximation
        # In practice, you would use the full Hilbert curve implementation
        x, y, z = coords[:, 0], coords[:, 1], coords[:, 2]

        # Interleave bits for Z-order (Morton code) as Hilbert approximation
        def interleave_bits(x, y, z):
            # Simple bit interleaving for 3D Morton code
            result = torch.zeros_like(x, dtype=torch.int64)
            for i in range(depth):
                bit_x = (x >> i) & 1
                bit_y = (y >> i) & 1
                bit_z = (z >> i) & 1
                result |= (bit_x << (3 * i)) | (bit_y << (3 * i + 1)) | (bit_z << (3 * i + 2))
            return result

        return interleave_bits(x.long(), y.long(), z.long())


class OrderScale(nn.Module):
    """Lightweight order-specific scaling"""
    
    def __init__(self, dim):
        super().__init__()
        self.gamma = nn.Parameter(torch.ones(dim))
        self.beta = nn.Parameter(torch.zeros(dim))
        nn.init.normal_(self.gamma, mean=1.0, std=0.02)
        nn.init.normal_(self.beta, std=0.02)
    
    def forward(self, x):
        return x * self.gamma + self.beta


class ParametricSemanticBackend(nn.Module):
    """Parametric backend using Mamba blocks for sequence modeling"""
    
    def __init__(self, d_model, n_layer, rms_norm=False, drop_path=0.1):
        super().__init__()
        
        # Create drop path rates
        dpr = [x.item() for x in torch.linspace(0, drop_path, n_layer)]
        
        # Mamba blocks
        self.layers = nn.ModuleList([
            self._create_mamba_block(
                d_model=d_model,
                layer_idx=i,
                rms_norm=rms_norm,
                drop_path=dpr[i]
            ) for i in range(n_layer)
        ])
        
        # Final normalization
        self.norm = (nn.LayerNorm if not rms_norm else RMSNorm)(d_model)
    
    def _create_mamba_block(self, d_model, layer_idx, rms_norm, drop_path):
        """Create a single Mamba block"""
        mixer_cls = partial(Mamba, layer_idx=layer_idx)
        norm_cls = partial(nn.LayerNorm if not rms_norm else RMSNorm, eps=1e-5)

        block = Block(
            d_model,
            mixer_cls,
            norm_cls=norm_cls,
            fused_add_norm=False,
            residual_in_fp32=False,
            drop_path=drop_path,
        )
        block.layer_idx = layer_idx
        return block
    
    def forward(self, x):
        """
        Args:
            x: [B, L, D] - input sequence
        Returns:
            x: [B, L, D] - contextualized features
        """
        for layer in self.layers:
            x = layer(x)
        
        x = self.norm(x)
        return x





# Example configuration class
class HybridConfig:
    def __init__(self):
        # Frontend parameters (Point-NN inspired)
        self.input_points = 2048
        self.frontend_stages = 2
        self.frontend_embed_dim = 128
        self.k_neighbors = 32
        self.alpha = 100
        self.beta = 1000
        
        # Backend parameters (Mamba inspired)
        self.backend_dim = 384
        self.backend_depth = 12
        self.rms_norm = False
        self.drop_path = 0.1
        
        # Task parameters
        self.num_classes = 40  # For ModelNet40


# Example usage
def create_hybrid_model():
    config = HybridConfig()
    model = HybridPointNNMamba(config)
    return model


if __name__ == "__main__":
    # Test the model
    model = create_hybrid_model()
    batch_size = 4
    num_points = 2048
    
    # Create dummy input
    points = torch.randn(batch_size, num_points, 3)
    
    # Forward pass
    with torch.no_grad():
        logits = model(points)
        print(f"Input shape: {points.shape}")
        print(f"Output shape: {logits.shape}")
        print("Model created successfully!")
